#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文件夹管理核心模块
实现基于SKU列表的文件夹批量导出功能
"""

import os
import sys
import pandas as pd
import shutil
from pathlib import Path
import logging
import time
import datetime
from typing import List, Tuple, Dict, Optional

# 配置日志
logger = logging.getLogger('FolderManager')


class FolderManager:
    """
    文件夹管理器
    负责根据SKU列表批量导出文件夹
    """
    
    def __init__(self):
        """初始化文件夹管理器"""
        self.processed_folders = []
        self.failed_folders = []
        self.export_stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'start_time': None,
            'end_time': None
        }
    
    def read_sku_from_excel(self, excel_path: str) -> List[str]:
        """
        从Excel文件中读取SKU列表
        
        Args:
            excel_path: Excel文件路径
            
        Returns:
            SKU列表
        """
        try:
            logger.info(f"开始读取Excel文件: {excel_path}")
            
            # 尝试读取Excel文件
            df = pd.read_excel(excel_path)
            logger.info(f"Excel文件读取成功，共 {len(df)} 行数据")
            
            # 查找SKU列
            sku_column = None
            for col in df.columns:
                if 'SKU' in str(col).upper():
                    sku_column = col
                    break
            
            if sku_column is None:
                raise ValueError("未找到SKU列，请确保Excel文件包含名为'SKU'的列")
            
            logger.info(f"找到SKU列: {sku_column}")
            
            # 提取SKU数据并清理
            sku_list = df[sku_column].dropna().astype(str).str.strip().tolist()
            sku_list = [sku for sku in sku_list if sku and sku != 'nan']
            
            logger.info(f"成功提取 {len(sku_list)} 个有效SKU")
            return sku_list
            
        except Exception as e:
            error_msg = f"读取Excel文件失败: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    def find_sku_folder(self, root_dir: str, sku: str) -> Optional[str]:
        """
        递归搜索包含指定SKU的文件夹

        Args:
            root_dir: 搜索的根目录
            sku: 要搜索的SKU字符串

        Returns:
            找到的文件夹路径，如果未找到则返回None
        """
        sku = str(sku).strip()
        logger.info(f"🔍 开始搜索SKU文件夹: '{sku}' 在路径: {root_dir}")

        # 首先检查直接匹配（传统方式）
        direct_path = os.path.join(root_dir, sku)
        logger.info(f"📁 检查直接路径: {direct_path}")
        if os.path.isdir(direct_path):
            logger.info(f"✅ 直接匹配成功: {direct_path}")
            return direct_path
        else:
            logger.info(f"❌ 直接路径不存在，开始递归搜索...")

        # 如果没有直接匹配，开始递归搜索
        try:
            search_count = 0
            for root, dirs, files in os.walk(root_dir):
                search_count += 1
                if search_count <= 5:  # 只显示前5个搜索路径
                    logger.info(f"🔍 搜索目录 {search_count}: {root}")
                    logger.info(f"   📂 包含子文件夹: {dirs[:10]}...")  # 只显示前10个

                for dir_name in dirs:
                    # 检查完全匹配
                    if dir_name == sku:
                        full_path = os.path.join(root, dir_name)
                        logger.info(f"✅ 找到SKU文件夹: {full_path}")
                        return full_path

            logger.info(f"📊 总共搜索了 {search_count} 个目录")
        except Exception as e:
            logger.error(f"❌ 搜索SKU {sku} 时发生错误: {str(e)}")

        logger.warning(f"❌ 未找到SKU文件夹: {sku}")
        return None

    def find_image_type_folders(self, source_path: str, sku: str, image_types: List[str]) -> Dict[str, str]:
        """
        根据图片类型查找对应的文件夹

        Args:
            source_path: 源文件夹路径
            sku: SKU名称
            image_types: 图片类型列表 ['白底图', '场景图', '色块图']

        Returns:
            字典，键为图片类型，值为对应的文件夹路径
        """
        found_folders = {}

        logger.info(f"🔍 开始查找SKU '{sku}' 的图片类型文件夹，类型: {image_types}")

        # 首先找到SKU的基础文件夹
        sku_base_folder = self.find_sku_folder(source_path, sku)
        logger.info(f"📁 SKU基础文件夹查找结果: {sku_base_folder}")

        if not sku_base_folder:
            logger.warning(f"❌ 未找到SKU '{sku}' 的基础文件夹")
            return found_folders

        for image_type in image_types:
            logger.info(f"🔍 正在查找图片类型: {image_type}")

            if image_type == '色块图':
                # 色块图在SKU文件夹的上1层目录下的色块文件夹中
                parent_dir = os.path.dirname(sku_base_folder)
                color_folder = os.path.join(parent_dir, '色块')
                logger.info(f"🎨 色块图查找路径: {color_folder}")

                if os.path.exists(color_folder):
                    found_folders[image_type] = color_folder
                    logger.info(f"✅ 找到色块图文件夹: {color_folder}")
                else:
                    logger.warning(f"❌ 色块图文件夹不存在: {color_folder}")
            else:
                # 白底图和场景图：在SKU文件夹内部查找
                image_type_folder = os.path.join(sku_base_folder, image_type)
                logger.info(f"📸 {image_type}查找路径: {image_type_folder}")

                if os.path.exists(image_type_folder):
                    found_folders[image_type] = image_type_folder
                    logger.info(f"✅ 找到{image_type}文件夹: {image_type_folder}")
                else:
                    # 尝试查找带连字符的命名方式：SKU-图片类型
                    alt_folder_name = f"{sku}-{image_type}"
                    alt_folder_path = self.find_sku_folder(source_path, alt_folder_name)
                    logger.info(f"🔄 尝试备用命名方式: {alt_folder_name} -> {alt_folder_path}")

                    if alt_folder_path:
                        found_folders[image_type] = alt_folder_path
                        logger.info(f"✅ 通过备用方式找到{image_type}文件夹: {alt_folder_path}")
                    else:
                        logger.warning(f"❌ {image_type}文件夹不存在，已尝试路径:")
                        logger.warning(f"   - 标准路径: {image_type_folder}")
                        logger.warning(f"   - 备用路径: {alt_folder_name}")

        logger.info(f"📋 SKU '{sku}' 图片类型查找完成，找到 {len(found_folders)} 种类型: {list(found_folders.keys())}")
        return found_folders

    def create_export_subfolder(self, export_path: str) -> str:
        """
        在导出目标文件夹中创建一个新的子文件夹用于存放所有复制的文件夹
        
        Args:
            export_path: 导出目标路径
            
        Returns:
            创建的子文件夹路径
        """
        now = datetime.datetime.now()
        subfolder_name = f"导出SKU_{now.strftime('%Y%m%d_%H%M%S')}"
        subfolder_path = os.path.join(export_path, subfolder_name)
        
        try:
            os.makedirs(subfolder_path, exist_ok=True)
            logger.info(f"创建导出子文件夹: {subfolder_path}")
            return subfolder_path
        except Exception as e:
            error_msg = f"创建导出子文件夹时发生错误: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    def copy_folders(self, source_path: str, export_path: str, sku_list: List[str],
                    progress_callback=None, image_types: List[str] = None) -> Tuple[List[str], List[str], str]:
        """
        查找并复制指定SKU的文件夹及其内容到目标位置

        Args:
            source_path: 源文件夹路径
            export_path: 导出目标路径
            sku_list: SKU列表
            progress_callback: 进度回调函数
            image_types: 图片类型列表，如果为None则复制整个SKU文件夹

        Returns:
            (成功复制的文件夹列表, 详细错误信息列表, 导出子文件夹路径)
        """
        copied_folders = []
        errors = []
        failed_skus = []  # 专门记录失败的SKU信息
        total_folders = len(sku_list)

        # 初始化统计信息
        self.export_stats = {
            'total': total_folders,
            'success': 0,
            'failed': 0,
            'failed_skus': [],  # 新增：失败SKU详细信息
            'start_time': time.time(),
            'end_time': None
        }
        
        logger.info(f"开始查找和复制文件夹，共 {total_folders} 个SKU")
        if image_types:
            logger.info(f"选择的图片类型: {image_types}")

        # 在导出目标文件夹中创建一个新的子文件夹用于存放所有复制的文件夹
        export_subfolder = self.create_export_subfolder(export_path)
        logger.info(f"所有SKU文件夹将被复制到: {export_subfolder}")
        
        for i, sku in enumerate(sku_list):
            try:
                # 确保SKU是字符串
                sku_str = str(sku).strip()
                if not sku_str:
                    continue
                
                # 更新进度
                if progress_callback:
                    progress_callback(i, total_folders, f"正在查找: {sku_str}")
                
                logger.info(f"处理 ({i+1}/{total_folders}): {sku_str}")

                if image_types:
                    # 根据图片类型查找对应的文件夹
                    found_folders = self.find_image_type_folders(source_path, sku_str, image_types)

                    if not found_folders:
                        # 详细的失败信息
                        failed_info = {
                            'sku': sku_str,
                            'reason': '未找到任何图片类型文件夹',
                            'details': f"在路径 '{source_path}' 中未找到SKU '{sku_str}' 的任何图片类型文件夹 ({', '.join(image_types)})",
                            'suggestion': '请检查SKU是否存在，或者图片类型文件夹是否正确命名'
                        }
                        failed_skus.append(failed_info)
                        self.export_stats['failed_skus'].append(failed_info)

                        error_msg = f"❌ SKU '{sku_str}': {failed_info['reason']}"
                        errors.append(error_msg)
                        logger.warning(f"{error_msg} - {failed_info['details']}")
                        self.export_stats['failed'] += 1
                        continue

                    # 为每个SKU创建目标文件夹
                    target_sku_folder = os.path.join(export_subfolder, sku_str)

                    # 检查目标文件夹是否已存在
                    if os.path.exists(target_sku_folder):
                        failed_info = {
                            'sku': sku_str,
                            'reason': '目标位置已存在同名文件夹',
                            'details': f"目标路径 '{target_sku_folder}' 已存在",
                            'suggestion': '请检查是否重复导出，或清理目标文件夹'
                        }
                        failed_skus.append(failed_info)
                        self.export_stats['failed_skus'].append(failed_info)

                        error_msg = f"❌ SKU '{sku_str}': {failed_info['reason']}"
                        errors.append(error_msg)
                        logger.warning(f"{error_msg} - {failed_info['details']}")
                        self.export_stats['failed'] += 1
                        continue

                    # 创建SKU目标文件夹
                    os.makedirs(target_sku_folder, exist_ok=True)

                    # 更新状态
                    if progress_callback:
                        progress_callback(i, total_folders, f"正在复制: {sku_str} ({len(found_folders)}种图片类型)")

                    # 复制每种图片类型的文件夹 - 优化并行复制
                    copy_start = time.time()
                    sku_success = True
                    copy_errors = []

                    for image_type, source_folder in found_folders.items():
                        try:
                            if image_type == '色块图':
                                # 色块图直接复制到SKU文件夹下，命名为"色块图"
                                target_type_folder = os.path.join(target_sku_folder, '色块图')
                            else:
                                # 白底图和场景图保持原有命名
                                target_type_folder = os.path.join(target_sku_folder, image_type)

                            # 使用优化的复制方法
                            self._copy_folder_optimized(source_folder, target_type_folder)
                            logger.info(f"✅ 复制{image_type}完成: {source_folder} -> {target_type_folder}")

                        except Exception as e:
                            copy_error = f"复制{image_type}失败: {str(e)}"
                            copy_errors.append(copy_error)
                            logger.error(f"❌ {copy_error}")
                            sku_success = False

                    copy_end = time.time()
                    copy_time = copy_end - copy_start

                    if sku_success:
                        logger.info(f"✅ SKU '{sku_str}' 复制完成，耗时: {copy_time:.2f}秒")
                        copied_folders.append(sku_str)
                        self.export_stats['success'] += 1
                    else:
                        # 记录部分失败的SKU
                        failed_info = {
                            'sku': sku_str,
                            'reason': '部分图片类型复制失败',
                            'details': '; '.join(copy_errors),
                            'suggestion': '请检查网络连接和磁盘空间，或重试该SKU'
                        }
                        failed_skus.append(failed_info)
                        self.export_stats['failed_skus'].append(failed_info)
                        self.export_stats['failed'] += 1

                else:
                    # 原有逻辑：复制整个SKU文件夹
                    source_folder = self.find_sku_folder(source_path, sku_str)

                    # 检查源文件夹是否找到
                    if not source_folder:
                        failed_info = {
                            'sku': sku_str,
                            'reason': '未找到SKU基础文件夹',
                            'details': f"在路径 '{source_path}' 中未找到SKU '{sku_str}' 的基础文件夹",
                            'suggestion': '请检查SKU是否存在，或者路径是否正确'
                        }
                        failed_skus.append(failed_info)
                        self.export_stats['failed_skus'].append(failed_info)

                        error_msg = f"❌ SKU '{sku_str}': {failed_info['reason']}"
                        errors.append(error_msg)
                        logger.warning(f"{error_msg} - {failed_info['details']}")
                        self.export_stats['failed'] += 1
                        continue

                    # 目标文件夹路径 (在新创建的子文件夹中)
                    target_folder = os.path.join(export_subfolder, sku_str)

                    # 检查目标文件夹是否已存在
                    if os.path.exists(target_folder):
                        failed_info = {
                            'sku': sku_str,
                            'reason': '目标位置已存在同名文件夹',
                            'details': f"目标路径 '{target_folder}' 已存在",
                            'suggestion': '请检查是否重复导出，或清理目标文件夹'
                        }
                        failed_skus.append(failed_info)
                        self.export_stats['failed_skus'].append(failed_info)

                        error_msg = f"❌ SKU '{sku_str}': {failed_info['reason']}"
                        errors.append(error_msg)
                        logger.warning(f"{error_msg} - {failed_info['details']}")
                        self.export_stats['failed'] += 1
                        continue

                    # 更新状态
                    if progress_callback:
                        progress_callback(i, total_folders, f"正在复制: {sku_str}")

                    # 复制文件夹及其内容 - 使用优化方法
                    logger.info(f"开始复制文件夹: {sku_str}")
                    copy_start = time.time()
                    self._copy_folder_optimized(source_folder, target_folder)
                    copy_end = time.time()
                    copy_time = copy_end - copy_start

                    logger.info(f"✅ 文件夹 '{sku_str}' 复制完成，耗时: {copy_time:.2f}秒")
                    copied_folders.append(sku_str)
                    self.export_stats['success'] += 1
                
            except Exception as e:
                failed_info = {
                    'sku': sku_str,
                    'reason': '复制过程中发生异常',
                    'details': f"错误详情: {str(e)}",
                    'suggestion': '请检查网络连接、磁盘空间或文件权限，然后重试'
                }
                failed_skus.append(failed_info)
                self.export_stats['failed_skus'].append(failed_info)

                error_msg = f"❌ SKU '{sku_str}': {failed_info['reason']}"
                errors.append(error_msg)
                logger.error(f"{error_msg} - {failed_info['details']}", exc_info=True)
                self.export_stats['failed'] += 1
        
        # 完成所有复制
        self.export_stats['end_time'] = time.time()
        total_time = self.export_stats['end_time'] - self.export_stats['start_time']
        logger.info(f"所有文件夹复制完成，总耗时: {total_time:.2f}秒")
        
        # 更新最终进度
        if progress_callback:
            progress_callback(total_folders, total_folders, f"完成! 总耗时: {total_time:.2f}秒")
        
        return copied_folders, errors, export_subfolder

    def _copy_folder_optimized(self, source_folder: str, target_folder: str):
        """
        优化的文件夹复制方法，提高网络存储的复制效率

        Args:
            source_folder: 源文件夹路径
            target_folder: 目标文件夹路径
        """
        try:
            # 对于网络存储，使用更高效的复制策略
            if source_folder.startswith('\\\\') or ':' in source_folder:
                # 网络路径或映射驱动器，使用优化参数
                shutil.copytree(
                    source_folder,
                    target_folder,
                    copy_function=shutil.copy2,  # 使用更快的复制函数
                    ignore_dangling_symlinks=True,  # 忽略损坏的符号链接
                    dirs_exist_ok=False  # 确保目标不存在
                )
            else:
                # 本地路径，使用标准复制
                shutil.copytree(source_folder, target_folder)

        except Exception as e:
            # 如果优化复制失败，尝试标准复制
            logger.warning(f"优化复制失败，尝试标准复制: {str(e)}")
            shutil.copytree(source_folder, target_folder)
    
    def get_export_stats(self) -> Dict:
        """
        获取导出统计信息
        
        Returns:
            统计信息字典
        """
        stats = self.export_stats.copy()
        if stats['start_time'] and stats['end_time']:
            stats['total_time'] = stats['end_time'] - stats['start_time']
        else:
            stats['total_time'] = 0
        return stats
